{% extends 'base.html' %}

{% block title %}Home - Sangguniang Bayan Ordinance System{% endblock %}

{% block extra_head %}
<!-- AOS Animation Library -->
<link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
<script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
<!-- Font Awesome for Icons -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
{% endblock %}

{% block content %}
<!-- Announcement Banner -->
<div id="announcement-banner" class="bg-yellow-400 text-yellow-900 py-2 relative border-b-2 border-yellow-500">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-2 flex-1">
                <i class="fas fa-exclamation-triangle text-yellow-700"></i>
                <div class="announcement-content">
                    <span class="font-semibold text-sm">Important Notice:</span>
                    <span class="ml-2 text-sm">Public Hearing on Environmental Protection Ordinance - December 15, 2024, 9:00 AM at Municipal Hall</span>
                </div>
            </div>
            <button onclick="dismissBanner()" class="text-yellow-700 hover:text-yellow-900 transition-colors ml-4 p-1">
                <i class="fas fa-times text-sm"></i>
            </button>
        </div>
    </div>
</div>

<!-- Hero Section -->
<div class="relative bg-primary-offwhite text-primary-dark-blue">
    <div class="hero-content max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div class="w-full">
            <div class="text-center" data-aos="fade-up" data-aos-duration="1000">
                <h1 class="text-4xl md:text-5xl font-extrabold mb-4 text-primary-dark-blue" data-aos="fade-up" data-aos-delay="200">
                    Municipality of Dumingag<br>
                    <span class="text-2xl md:text-3xl font-medium">Public Ordinances Portal</span>
                </h1>

                <p class="text-base md:text-lg mb-6 text-primary-black max-w-2xl mx-auto leading-relaxed font-normal" data-aos="fade-up" data-aos-delay="400">
                    Access and search through our comprehensive collection of municipal ordinances.
                    Stay informed about local laws and regulations that shape our community.
                </p>

                <!-- Statistics Cards -->
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8 max-w-4xl mx-auto" data-aos="fade-up" data-aos-delay="600">
                    <div class="bg-white rounded-xl p-4 text-center shadow-lg border border-primary-beige">
                        <i class="fas fa-file-alt text-2xl text-primary-dark-blue mb-2"></i>
                        <div class="stats-counter text-2xl" data-count="{{ total_ordinances }}">0</div>
                        <div class="text-primary-black text-sm">Total Ordinances</div>
                    </div>
                    <div class="bg-white rounded-xl p-4 text-center shadow-lg border border-primary-beige">
                        <i class="fas fa-tags text-2xl text-primary-dark-blue mb-2"></i>
                        <div class="stats-counter text-2xl" data-count="{{ total_categories }}">0</div>
                        <div class="text-primary-black text-sm">Categories</div>
                    </div>
                    <div class="bg-white rounded-xl p-4 text-center shadow-lg border border-primary-beige">
                        <i class="fas fa-users text-2xl text-primary-dark-blue mb-2"></i>
                        <div class="stats-counter text-2xl" data-count="{{ top_sponsors.count }}">0</div>
                        <div class="text-primary-black text-sm">Council Members</div>
                    </div>
                </div>

                <div class="flex flex-col sm:flex-row items-center justify-center gap-4" data-aos="fade-up" data-aos-delay="800">
                    <a href="{% url 'ordinances:ordinance_list' %}"
                       class="btn-primary px-8 py-4 rounded-xl font-semibold transition-all duration-300 transform hover:scale-105 shadow-lg">
                        <i class="fas fa-search mr-2"></i>Browse Ordinances
                    </a>

                    <!-- Arrow between buttons -->
                    <div class="hidden sm:block text-primary-dark-blue">
                        <i class="fas fa-arrow-right text-xl"></i>
                    </div>
                    <div class="block sm:hidden text-primary-dark-blue">
                        <i class="fas fa-arrow-down text-xl"></i>
                    </div>

                    <a href="#officials"
                       class="btn-secondary px-8 py-4 rounded-xl font-semibold transition-all duration-300 transform hover:scale-105">
                        <i class="fas fa-user-tie mr-2"></i>Meet Our Officials
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Scroll Indicator -->
    <div class="absolute bottom-8 left-1/2 transform -translate-x-1/2 text-primary-dark-blue animate-bounce">
        <i class="fas fa-chevron-down text-2xl"></i>
    </div>
</div>

<!-- Officials Section -->
<section id="officials" class="py-20 bg-gradient-to-br from-primary-offwhite to-primary-beige">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16" data-aos="fade-up">
            <!-- Section Header with Logo -->
            <div class="flex items-center justify-center mb-6">
                {% comment %} <div class="w-16 h-16 mr-4">
                    <img src="{% load static %}{% static 'img/dumingag-logo.png' %}"
                         alt="Dumingag Logo"
                         class="w-full h-full object-contain opacity-80">
                </div> {% endcomment %}
                <h2 class="text-4xl font-bold text-primary-dark-blue">
                    Our Municipal Leaders
                </h2>
                {% comment %} <div class="w-16 h-16 ml-4">
                    <img src="{% load static %}{% static 'img/dumingag-logo.png' %}"
                         alt="Dumingag Logo"
                         class="w-full h-full object-contain opacity-80">
                </div> {% endcomment %}
            </div>
            <p class="text-xl text-primary-black max-w-3xl mx-auto">
                Meet the dedicated officials serving the Municipality of Dumingag with integrity and commitment to public service.
            </p>
        </div>

        <!-- Mayor and Vice Mayor -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 mb-16">
            <!-- Mayor -->
            {% if officials.mayor %}
                <div class="official-card card-primary rounded-2xl shadow-xl overflow-hidden" data-aos="fade-right" data-aos-delay="200">
                    <div class="relative">
                        <div class="h-64 bg-gradient-to-br from-primary-dark-blue to-primary-black flex items-center justify-center">
                            <!-- Logo Watermark -->
                            <div class="absolute inset-0 flex items-center justify-center opacity-10">
                                <img src="{% load static %}{% static 'img/dumingag-logo.png' %}"
                                     alt="Dumingag Logo"
                                     class="w-48 h-48 object-contain">
                            </div>
                            <div class="w-32 h-32 bg-primary-offwhite rounded-full flex items-center justify-center relative z-10 overflow-hidden">
                                {% if officials.mayor.profile_picture %}
                                    <img src="{{ officials.mayor.profile_picture.url }}"
                                         alt="{{ officials.mayor.name }}"
                                         class="w-full h-full object-cover">
                                {% else %}
                                    <i class="fas fa-user-tie text-6xl text-primary-dark-blue"></i>
                                {% endif %}
                            </div>
                        </div>
                        <div class="absolute top-4 right-4 bg-primary-beige text-primary-dark-blue px-3 py-1 rounded-full text-sm font-semibold">
                            <i class="fas fa-crown mr-1"></i>Mayor
                        </div>
                    </div>
                    <div class="p-8">
                        <h3 class="text-2xl font-bold text-primary-dark-blue mb-2">{{ officials.mayor.name }}</h3>
                        <p class="text-primary-black font-semibold mb-4">{{ officials.mayor.get_position_display }}</p>
                        {% if officials.mayor.bio %}
                            <p class="text-gray-600 mb-6">{{ officials.mayor.bio }}</p>
                        {% endif %}
                        {% if officials.mayor.get_achievements_list %}
                            <div class="space-y-2">
                                <h4 class="font-semibold text-primary-dark-blue">Key Achievements:</h4>
                                {% for achievement in officials.mayor.get_achievements_list %}
                                    <div class="flex items-center text-sm text-gray-600">
                                        <i class="fas fa-check-circle text-primary-beige mr-2"></i>
                                        {{ achievement }}
                                    </div>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                </div>
            {% else %}
                <div class="card-secondary rounded-2xl shadow-xl p-8 text-center" data-aos="fade-right" data-aos-delay="200">
                    <i class="fas fa-user-plus text-4xl text-gray-400 mb-4"></i>
                    <h3 class="text-xl font-bold text-gray-600 mb-2">Mayor Position</h3>
                    <p class="text-gray-500">No mayor information available</p>
                </div>
            {% endif %}

            <!-- Vice Mayor -->
            {% if officials.vice_mayor %}
                <div class="official-card card-primary rounded-2xl shadow-xl overflow-hidden" data-aos="fade-left" data-aos-delay="400">
                    <div class="relative">
                        <div class="h-64 bg-gradient-to-br from-primary-beige to-primary-dark-blue flex items-center justify-center">
                            <!-- Logo Watermark -->
                            <div class="absolute inset-0 flex items-center justify-center opacity-10">
                                <img src="{% load static %}{% static 'img/dumingag-logo.png' %}"
                                     alt="Dumingag Logo"
                                     class="w-48 h-48 object-contain">
                            </div>
                            <div class="w-32 h-32 bg-primary-offwhite rounded-full flex items-center justify-center relative z-10 overflow-hidden">
                                {% if officials.vice_mayor.profile_picture %}
                                    <img src="{{ officials.vice_mayor.profile_picture.url }}"
                                         alt="{{ officials.vice_mayor.name }}"
                                         class="w-full h-full object-cover">
                                {% else %}
                                    <i class="fas fa-user-tie text-6xl text-primary-dark-blue"></i>
                                {% endif %}
                            </div>
                        </div>
                        <div class="absolute top-4 right-4 bg-primary-beige text-primary-dark-blue px-3 py-1 rounded-full text-sm font-semibold">
                            <i class="fas fa-star mr-1"></i>Vice Mayor
                        </div>
                    </div>
                    <div class="p-8">
                        <h3 class="text-2xl font-bold text-primary-dark-blue mb-2">{{ officials.vice_mayor.name }}</h3>
                        <p class="text-primary-black font-semibold mb-4">{{ officials.vice_mayor.get_position_display }}</p>
                        {% if officials.vice_mayor.bio %}
                            <p class="text-gray-600 mb-6">{{ officials.vice_mayor.bio }}</p>
                        {% endif %}
                        {% if officials.vice_mayor.get_achievements_list %}
                            <div class="space-y-2">
                                <h4 class="font-semibold text-primary-dark-blue">Key Achievements:</h4>
                                {% for achievement in officials.vice_mayor.get_achievements_list %}
                                    <div class="flex items-center text-sm text-gray-600">
                                        <i class="fas fa-check-circle text-primary-beige mr-2"></i>
                                        {{ achievement }}
                                    </div>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                </div>
            {% else %}
                <div class="card-secondary rounded-2xl shadow-xl p-8 text-center" data-aos="fade-left" data-aos-delay="400">
                    <i class="fas fa-user-plus text-4xl text-gray-400 mb-4"></i>
                    <h3 class="text-xl font-bold text-gray-600 mb-2">Vice Mayor Position</h3>
                    <p class="text-gray-500">No vice mayor information available</p>
                </div>
            {% endif %}
        </div>

        <!-- Council Members -->
        <div class="mb-12" data-aos="fade-up" data-aos-delay="600">
            <h3 class="text-3xl font-bold text-center text-primary-dark-blue mb-8">
                <i class="fas fa-users text-primary-beige mr-3"></i>
                Sangguniang Bayan Members
            </h3>
            {% if officials.council_members %}
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                    {% for member in officials.council_members %}
                        <div class="official-card card-primary rounded-xl shadow-lg overflow-hidden" data-aos="zoom-in" data-aos-delay="{{ forloop.counter|add:600 }}">
                            <div class="h-48 bg-gradient-to-br from-primary-beige to-primary-dark-blue flex items-center justify-center">
                                <div class="w-20 h-20 bg-primary-offwhite rounded-full flex items-center justify-center overflow-hidden">
                                    {% if member.profile_picture %}
                                        <img src="{{ member.profile_picture.url }}"
                                             alt="{{ member.name }}"
                                             class="w-full h-full object-cover">
                                    {% else %}
                                        <i class="fas fa-user text-4xl text-primary-dark-blue"></i>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="p-6">
                                <h4 class="text-lg font-bold text-primary-dark-blue mb-1">{{ member.name }}</h4>
                                <p class="text-primary-black text-sm font-semibold mb-2">{{ member.get_position_display }}</p>
                                {% if member.committee %}
                                    <p class="text-gray-600 text-sm">{{ member.committee }}</p>
                                {% endif %}
                            </div>
                        </div>
                    {% endfor %}
                </div>
            {% else %}
                <div class="text-center py-12">
                    <i class="fas fa-users text-6xl text-gray-300 mb-4"></i>
                    <h4 class="text-xl font-bold text-gray-600 mb-2">No Council Members</h4>
                    <p class="text-gray-500">Council member information will be displayed here when available.</p>
                </div>
            {% endif %}
        </div>
    </div>
</section>

<!-- Search Section -->
<div id="search" class="bg-primary-offwhite py-16">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12" data-aos="fade-up">
            <h2 class="text-4xl font-bold text-primary-dark-blue mb-4">
                <i class="fas fa-search text-primary-beige mr-3"></i>
                Search Ordinances
            </h2>
            <p class="text-xl text-primary-black">Find specific ordinances by keyword, number, or category</p>
        </div>

        <div class="max-w-4xl mx-auto" data-aos="fade-up" data-aos-delay="200">
            <form action="{% url 'ordinances:ordinance_list' %}" method="get" class="space-y-6">
                <!-- Main Search -->
                <div class="relative">
                    <input type="text"
                           name="search"
                           placeholder="Search by title, content, or ordinance number..."
                           class="form-input w-full px-6 py-4 text-lg rounded-xl transition-all duration-300">
                    <button type="submit"
                            class="absolute right-2 top-2 btn-primary px-6 py-2 rounded-lg transition-all duration-300 transform hover:scale-105">
                        <i class="fas fa-search mr-2"></i>Search
                    </button>
                </div>

                <!-- Enhanced Filters -->
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div class="relative">
                        <i class="fas fa-tags absolute left-3 top-4 text-gray-400"></i>
                        <select name="category" class="form-input w-full pl-10 pr-4 py-3 rounded-xl appearance-none">
                            <option value="">All Categories</option>
                            {% for category in categories %}
                                <option value="{{ category.slug }}">{{ category.name }}</option>
                            {% endfor %}
                        </select>
                    </div>

                    <div class="relative">
                        <i class="fas fa-calendar absolute left-3 top-4 text-gray-400"></i>
                        <select name="year" class="form-input w-full pl-10 pr-4 py-3 rounded-xl appearance-none">
                            <option value="">All Years</option>
                            <option value="2024">2024</option>
                            <option value="2023">2023</option>
                            <option value="2022">2022</option>
                            <option value="2021">2021</option>
                            <option value="2020">2020</option>
                        </select>
                    </div>

                    <div class="relative">
                        <i class="fas fa-flag absolute left-3 top-4 text-gray-400"></i>
                        <select name="status" class="form-input w-full pl-10 pr-4 py-3 rounded-xl appearance-none">
                            <option value="">All Status</option>
                            <option value="approved">Approved</option>
                            <option value="published">Published</option>
                        </select>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Recent Ordinances -->
<div class="bg-primary-beige py-16">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
            <h2 class="text-3xl font-bold text-primary-dark-blue mb-4">Recent Ordinances</h2>
            <p class="text-lg text-primary-black">Latest approved and published ordinances</p>
        </div>

        {% if recent_ordinances %}
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                {% for ordinance in recent_ordinances %}
                    <div class="card-primary rounded-lg shadow-md hover:shadow-lg transition-shadow">
                        <div class="p-6">
                            <div class="flex items-center justify-between mb-4">
                                <span class="bg-primary-dark-blue text-primary-offwhite text-xs font-semibold px-2.5 py-0.5 rounded">
                                    {{ ordinance.ordinance_number }}
                                </span>
                                <span class="text-sm text-gray-500">{{ ordinance.year_passed }}</span>
                            </div>

                            <h3 class="text-lg font-semibold text-primary-dark-blue mb-3 line-clamp-2">
                                {{ ordinance.title }}
                            </h3>

                            <p class="text-gray-600 text-sm mb-4 line-clamp-3">
                                {{ ordinance.content|truncatewords:20 }}
                            </p>

                            <div class="flex items-center justify-between">
                                <span class="text-xs text-gray-500">
                                    {% if ordinance.category %}{{ ordinance.category.name }}{% endif %}
                                </span>
                                <a href="{{ ordinance.get_absolute_url }}"
                                   class="text-primary-dark-blue hover:text-primary-black font-medium text-sm">
                                    Read More →
                                </a>
                            </div>
                        </div>
                    </div>
                {% endfor %}
            </div>

            <div class="text-center mt-12">
                <a href="{% url 'ordinances:ordinance_list' %}"
                   class="btn-primary px-8 py-3 rounded-lg font-semibold transition-colors">
                    View All Ordinances
                </a>
            </div>
        {% else %}
            <div class="text-center py-12">
                <div class="text-gray-400 mb-4">
                    <svg class="mx-auto h-16 w-16" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                              d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                </div>
                <h3 class="text-lg font-medium text-gray-900 mb-2">No ordinances available</h3>
                <p class="text-gray-600">Check back later for new ordinances.</p>
            </div>
        {% endif %}
    </div>
</div>

<!-- Important Notices Section -->
<div class="bg-primary-offwhite py-16">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12" data-aos="fade-up">
            <h2 class="text-3xl font-bold text-primary-dark-blue mb-4">
                <i class="fas fa-bullhorn text-primary-beige mr-3"></i>
                Important Notices & Announcements
            </h2>
            <p class="text-lg text-primary-black">Stay updated with the latest ordinances and municipal announcements</p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <!-- New Ordinances -->
            <div class="card-primary rounded-xl p-6 text-center shadow-lg hover:shadow-xl transition-shadow" data-aos="fade-up" data-aos-delay="100">
                <div class="bg-primary-dark-blue rounded-full p-4 w-16 h-16 mx-auto mb-4 flex items-center justify-center">
                    <i class="fas fa-file-plus text-2xl text-primary-offwhite"></i>
                </div>
                <h3 class="text-lg font-semibold text-primary-dark-blue mb-3">New Ordinances This Month</h3>
                <div class="text-2xl font-bold text-primary-dark-blue mb-2">{{ new_ordinances_count|default:"0" }}</div>
                <p class="text-sm text-gray-600 mb-4">Recently passed ordinances</p>
                <a href="{% url 'ordinances:ordinance_list' %}?year={% now 'Y' %}&month={% now 'm' %}"
                   class="text-primary-dark-blue hover:text-primary-black font-medium text-sm">
                    View New Ordinances →
                </a>
            </div>

            <!-- Public Hearings -->
            <div class="card-primary rounded-xl p-6 text-center shadow-lg hover:shadow-xl transition-shadow" data-aos="fade-up" data-aos-delay="200">
                <div class="bg-primary-dark-blue rounded-full p-4 w-16 h-16 mx-auto mb-4 flex items-center justify-center">
                    <i class="fas fa-calendar-alt text-2xl text-primary-offwhite"></i>
                </div>
                <h3 class="text-lg font-semibold text-primary-dark-blue mb-3">Public Hearings Schedule</h3>
                <div class="text-sm text-primary-black mb-2">Next Session:</div>
                <div class="text-lg font-bold text-primary-dark-blue mb-2">Every 2nd Monday</div>
                <p class="text-sm text-gray-600 mb-4">Municipal Hall, 9:00 AM</p>
                <a href="#" class="text-primary-dark-blue hover:text-primary-black font-medium text-sm">
                    View Schedule →
                </a>
            </div>

            <!-- Ordinance Updates -->
            <div class="card-primary rounded-xl p-6 text-center shadow-lg hover:shadow-xl transition-shadow" data-aos="fade-up" data-aos-delay="300">
                <div class="bg-primary-dark-blue rounded-full p-4 w-16 h-16 mx-auto mb-4 flex items-center justify-center">
                    <i class="fas fa-edit text-2xl text-primary-offwhite"></i>
                </div>
                <h3 class="text-lg font-semibold text-primary-dark-blue mb-3">Ordinance Updates</h3>
                <div class="text-2xl font-bold text-primary-dark-blue mb-2">{{ updated_ordinances_count|default:"0" }}</div>
                <p class="text-sm text-gray-600 mb-4">Recent amendments & revisions</p>
                <a href="{% url 'ordinances:ordinance_list' %}?status=updated"
                   class="text-primary-dark-blue hover:text-primary-black font-medium text-sm">
                    View Updates →
                </a>
            </div>

            <!-- Community Announcements -->
            <div class="card-primary rounded-xl p-6 text-center shadow-lg hover:shadow-xl transition-shadow" data-aos="fade-up" data-aos-delay="400">
                <div class="bg-primary-dark-blue rounded-full p-4 w-16 h-16 mx-auto mb-4 flex items-center justify-center">
                    <i class="fas fa-users text-2xl text-primary-offwhite"></i>
                </div>
                <h3 class="text-lg font-semibold text-primary-dark-blue mb-3">Community Announcements</h3>
                <div class="text-sm text-primary-black mb-2">Latest:</div>
                <div class="text-sm font-semibold text-primary-dark-blue mb-2">Public Consultation</div>
                <p class="text-sm text-gray-600 mb-4">Environmental ordinance review</p>
                <a href="#" class="text-primary-dark-blue hover:text-primary-black font-medium text-sm">
                    Read More →
                </a>
            </div>
        </div>

        <!-- Important Notice Banner -->
        <div class="mt-12 bg-gradient-to-r from-primary-dark-blue to-primary-black rounded-xl p-6 text-center" data-aos="fade-up" data-aos-delay="500">
            <div class="flex items-center justify-center mb-4">
                <i class="fas fa-exclamation-triangle text-3xl text-primary-beige mr-3"></i>
                <h3 class="text-xl font-bold text-primary-offwhite">Important Notice</h3>
            </div>
            <p class="text-primary-beige mb-4">
                All citizens are encouraged to participate in public hearings and provide feedback on proposed ordinances.
                Your voice matters in shaping our community's future.
            </p>
            <a href="#" class="btn-secondary px-6 py-2 rounded-lg font-semibold">
                Learn How to Participate
            </a>
        </div>
    </div>
</div>

<!-- Official Footer Section -->
<section class="footer-gradient text-primary-offwhite py-16">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center" data-aos="fade-up">
            <!-- Official Seal and Information -->
            <div class="flex flex-col md:flex-row items-center justify-center mb-8">
                <div class="w-24 h-24 md:w-32 md:h-32 mb-6 md:mb-0 md:mr-8">
                    <img src="{% load static %}{% static 'img/dumingag-logo.png' %}"
                         alt="Municipality of Dumingag Official Seal"
                         class="w-full h-full object-contain drop-shadow-lg">
                </div>
                <div class="text-center md:text-left">
                    <h3 class="text-2xl md:text-3xl font-bold mb-2">Municipality of Dumingag</h3>
                    <p class="text-primary-beige text-lg mb-1">Province of Zamboanga del Sur</p>
                    <p class="text-primary-beige text-sm">Republic of the Philippines</p>
                </div>
            </div>

            <!-- Mission Statement -->
            <div class="max-w-4xl mx-auto mb-8">
                <p class="text-lg text-primary-beige leading-relaxed">
                    Committed to transparent governance and public service excellence,
                    the Municipality of Dumingag strives to build a progressive,
                    sustainable, and inclusive community for all residents.
                </p>
            </div>

            <!-- Contact Information -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8 mb-8">
                <div class="text-center">
                    <i class="fas fa-map-marker-alt text-2xl text-primary-beige mb-2"></i>
                    <h4 class="font-semibold mb-1">Municipal Hall</h4>
                    <p class="text-primary-beige text-sm">Dumingag, Zamboanga del Sur</p>
                </div>
                <div class="text-center">
                    <i class="fas fa-phone text-2xl text-primary-beige mb-2"></i>
                    <h4 class="font-semibold mb-1">Contact Number</h4>
                    <p class="text-primary-beige text-sm">(062) XXX-XXXX</p>
                </div>
                <div class="text-center">
                    <i class="fas fa-envelope text-2xl text-primary-beige mb-2"></i>
                    <h4 class="font-semibold mb-1">Email Address</h4>
                    <p class="text-primary-beige text-sm"><EMAIL></p>
                </div>
            </div>

            <!-- Copyright -->
            <div class="border-t border-primary-beige pt-6">
                <p class="text-primary-beige text-sm">
                    © {% now "Y" %} Municipality of Dumingag. All rights reserved. |
                    Powered by Sangguniang Bayan Ordinance System
                </p>
            </div>
        </div>
    </div>
</section>

{% block extra_js %}
<!-- Home page specific JavaScript -->
<script src="{% static 'js/home.js' %}"></script>
{% endblock %}
{% endblock %}
