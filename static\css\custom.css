/* Custom CSS for Sangguniang Bayan Ordinance System */
/* Color Palette: #F1EFEC, #D4C9BE, #123458, #030303, gray800, offwhite */

/* Import Poppins Font */
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800;900&display=swap');

:root {
    --primary-offwhite: #F1EFEC;
    --primary-beige: #D4C9BE;
    --primary-dark-blue: #123458;
    --primary-black: #030303;
    --gray-800: #1f2937;
    --offwhite: #fafafa;
}

/* Apply Poppins font globally */
* {
    font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
}

/* Font weight classes for better typography */
.font-light { font-weight: 300; }
.font-normal { font-weight: 400; }
.font-medium { font-weight: 500; }
.font-semibold { font-weight: 600; }
.font-bold { font-weight: 700; }
.font-extrabold { font-weight: 800; }
.font-black { font-weight: 900; }

/* Base styles */
[x-cloak] {
    display: none !important;
}

.htmx-indicator {
    opacity: 0;
    transition: opacity 200ms ease-in;
}

.htmx-request .htmx-indicator {
    opacity: 1;
}

.htmx-request.htmx-indicator {
    opacity: 1;
}

/* Hero section styles */
.hero-content {
    position: relative;
}

.floating-animation {
    animation: float 6s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
}

/* Gradient text removed - using solid colors for cleaner look */

.glass-effect {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    border: 1px solid var(--primary-beige);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.official-card {
    transition: all 0.3s ease;
    transform-style: preserve-3d;
}

.official-card:hover {
    transform: translateY(-10px) rotateY(5deg);
    box-shadow: 0 20px 40px rgba(3, 3, 3, 0.2);
}

.stats-counter {
    font-size: 3rem;
    font-weight: bold;
    color: var(--primary-dark-blue);
}

/* Custom color classes */
.bg-primary-offwhite {
    background-color: var(--primary-offwhite);
}

.bg-primary-beige {
    background-color: var(--primary-beige);
}

.bg-primary-dark-blue {
    background-color: var(--primary-dark-blue);
}

.bg-primary-black {
    background-color: var(--primary-black);
}

.text-primary-offwhite {
    color: var(--primary-offwhite);
}

.text-primary-beige {
    color: var(--primary-beige);
}

.text-primary-dark-blue {
    color: var(--primary-dark-blue);
}

.text-primary-black {
    color: var(--primary-black);
}

.border-primary-beige {
    border-color: var(--primary-beige);
}

.border-primary-dark-blue {
    border-color: var(--primary-dark-blue);
}

/* Navigation styles with new color palette */
.nav-primary {
    background: linear-gradient(135deg, var(--primary-dark-blue), var(--primary-black));
}

.nav-link {
    color: var(--primary-offwhite);
    transition: color 0.3s ease;
}

.nav-link:hover {
    color: var(--primary-beige);
}

/* Button styles */
.btn-primary {
    background-color: var(--primary-dark-blue);
    color: var(--primary-offwhite);
    border: 1px solid var(--primary-dark-blue);
    transition: all 0.3s ease;
}

.btn-primary:hover {
    background-color: var(--primary-beige);
    color: var(--primary-dark-blue);
    border-color: var(--primary-beige);
}

.btn-secondary {
    background-color: var(--primary-beige);
    color: var(--primary-dark-blue);
    border: 1px solid var(--primary-beige);
    transition: all 0.3s ease;
}

.btn-secondary:hover {
    background-color: var(--primary-dark-blue);
    color: var(--primary-offwhite);
    border-color: var(--primary-dark-blue);
}

/* Hero background - removed gradient, using solid off-white */

/* Card styles */
.card-primary {
    background-color: var(--primary-offwhite);
    border: 1px solid var(--primary-beige);
    box-shadow: 0 4px 6px rgba(3, 3, 3, 0.1);
}

.card-secondary {
    background-color: var(--offwhite);
    border: 1px solid var(--primary-beige);
}

/* Footer styles */
.footer-gradient {
    background: linear-gradient(135deg, var(--primary-black), var(--primary-dark-blue));
}

/* Form styles */
.form-input {
    border: 2px solid var(--primary-beige);
    background-color: var(--primary-offwhite);
    color: var(--primary-black);
}

.form-input:focus {
    border-color: var(--primary-dark-blue);
    box-shadow: 0 0 0 3px rgba(18, 52, 88, 0.1);
}

/* Message styles */
.message-success {
    background-color: rgba(212, 201, 190, 0.2);
    border-color: var(--primary-beige);
    color: var(--primary-dark-blue);
}

.message-error {
    background-color: rgba(3, 3, 3, 0.1);
    border-color: var(--primary-black);
    color: var(--primary-black);
}

/* Loading indicator */
.loading-indicator {
    background-color: var(--primary-dark-blue);
    color: var(--primary-offwhite);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .stats-counter {
        font-size: 2rem;
    }

    .floating-animation {
        animation-duration: 4s;
    }
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
    .floating-animation,
    .official-card,
    .btn-primary,
    .btn-secondary {
        animation: none;
        transition: none;
    }
}

/* Print styles */
@media print {
    .hero-canvas,
    .floating-animation,
    .glass-effect {
        display: none;
    }

    .hero-content {
        background: white;
        color: black;
    }
}
