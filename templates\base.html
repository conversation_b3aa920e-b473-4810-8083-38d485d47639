<!DOCTYPE html>
<html lang="en" x-data="{ mobileMenuOpen: false }">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Sangguniang Bayan Ordinance System{% endblock %}</title>

    <!-- TailwindCSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'primary-offwhite': '#F1EFEC',
                        'primary-beige': '#D4C9BE',
                        'primary-dark-blue': '#123458',
                        'primary-black': '#030303',
                    }
                }
            }
        }
    </script>

    <!-- HTMX -->
    <script src="https://unpkg.com/htmx.org@1.9.10"></script>

    <!-- Alpine.js -->
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>

    <!-- Custom CSS -->
    {% load static %}
    <link rel="stylesheet" href="{% static 'css/custom.css' %}">

    {% block extra_head %}{% endblock %}
</head>
<body class="bg-primary-offwhite min-h-screen">
    <!-- Navigation -->
    <nav class="nav-primary shadow-lg">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <a href="{% url 'ordinances:home' %}" class="flex-shrink-0 flex items-center">
                        {% load static %}
                        <div class="w-10 h-10 mr-3">
                            <img src="{% static 'img/dumingag-logo.png' %}"
                                 alt="Dumingag Logo"
                                 class="w-full h-full object-contain">
                        </div>
                        <div>
                            <h1 class="text-primary-offwhite text-lg font-bold leading-tight">Municipality of Dumingag</h1>
                            <p class="text-primary-beige text-xs">Ordinance System</p>
                        </div>
                    </a>

                    <!-- Desktop Navigation -->
                    <div class="hidden md:ml-6 md:flex md:space-x-8">
                        <a href="{% url 'ordinances:home' %}"
                           class="nav-link px-3 py-2 rounded-md text-sm font-medium">
                            Home
                        </a>
                        <a href="{% url 'ordinances:ordinance_list' %}"
                           class="nav-link px-3 py-2 rounded-md text-sm font-medium">
                            Ordinances
                        </a>
                    </div>
                </div>

                <!-- Desktop Auth Links -->
                <div class="hidden md:flex md:items-center md:space-x-4">
                    {% if user.is_authenticated %}
                        <a href="{% url 'ordinances:admin_dashboard' %}"
                           class="nav-link px-3 py-2 rounded-md text-sm font-medium">
                            Admin Dashboard
                        </a>
                        <a href="{% url 'admin:index' %}"
                           class="btn-primary px-4 py-2 rounded-md text-sm font-medium">
                            Django Admin
                        </a>
                        <form method="post" action="{% url 'admin:logout' %}" class="inline">
                            {% csrf_token %}
                            <button type="submit"
                                    class="nav-link px-3 py-2 rounded-md text-sm font-medium">
                                Logout
                            </button>
                        </form>
                    {% else %}
                        <a href="{% url 'admin:login' %}"
                           class="btn-primary px-4 py-2 rounded-md text-sm font-medium">
                            Admin Login
                        </a>
                    {% endif %}
                </div>

                <!-- Mobile menu button -->
                <div class="md:hidden flex items-center">
                    <button @click="mobileMenuOpen = !mobileMenuOpen"
                            class="nav-link p-2 rounded-md">
                        <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                  d="M4 6h16M4 12h16M4 18h16" />
                        </svg>
                    </button>
                </div>
            </div>
        </div>

        <!-- Mobile Navigation -->
        <div x-show="mobileMenuOpen" x-cloak class="md:hidden bg-primary-dark-blue">
            <div class="px-2 pt-2 pb-3 space-y-1">
                <a href="{% url 'ordinances:home' %}"
                   class="nav-link block px-3 py-2 rounded-md text-base font-medium">
                    Home
                </a>
                <a href="{% url 'ordinances:ordinance_list' %}"
                   class="nav-link block px-3 py-2 rounded-md text-base font-medium">
                    Ordinances
                </a>
                {% if user.is_authenticated %}
                    <a href="{% url 'ordinances:admin_dashboard' %}"
                       class="nav-link block px-3 py-2 rounded-md text-base font-medium">
                        Admin Dashboard
                    </a>
                    <a href="{% url 'admin:index' %}"
                       class="nav-link block px-3 py-2 rounded-md text-base font-medium">
                        Django Admin
                    </a>
                {% else %}
                    <a href="{% url 'admin:login' %}"
                       class="nav-link block px-3 py-2 rounded-md text-base font-medium">
                        Admin Login
                    </a>
                {% endif %}
            </div>
        </div>
    </nav>

    <!-- Messages -->
    {% if messages %}
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 mt-4">
            {% for message in messages %}
                <div class="alert alert-{{ message.tags }} {% if message.tags == 'error' %}message-error{% elif message.tags == 'success' %}message-success{% else %}message-success{% endif %} px-4 py-3 rounded mb-4"
                     x-data="{ show: true }"
                     x-show="show"
                     x-transition>
                    <div class="flex justify-between items-center">
                        <span>{{ message }}</span>
                        <button @click="show = false" class="ml-4 text-lg font-bold" data-dismiss="message">&times;</button>
                    </div>
                </div>
            {% endfor %}
        </div>
    {% endif %}

    <!-- Main Content -->
    <main>
        {% block content %}
        {% endblock %}
    </main>

    <!-- Footer -->
    <footer class="footer-gradient text-primary-offwhite mt-16">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <div class="text-center">
                <p>&copy; {% now "Y" %} Sangguniang Bayan Ordinance System. All rights reserved.</p>
                <p class="mt-2 text-primary-beige">Powered by Django, HTMX, Alpine.js, and TailwindCSS</p>
            </div>
        </div>
    </footer>

    <!-- Loading indicator -->
    <div id="loading-indicator" class="htmx-indicator fixed top-4 right-4 loading-indicator px-4 py-2 rounded-lg shadow-lg z-50">
        <div class="flex items-center">
            <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-primary-offwhite" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            Loading...
        </div>
    </div>

    <!-- Base JavaScript -->
    <script src="{% static 'js/base.js' %}"></script>
    {% block extra_js %}{% endblock %}
</body>
</html>